import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Coordinate } from 'ol/coordinate';
import { CoordinateSystem, BaseMapCoordinateMapping, BusinessLayerConfigs } from '../map/layer.config';
import { LayerOption } from '../map/class/layer-option';

/**
 * 坐标转换结果接口
 */
export interface CoordinateTransformResult {
  coordinate: Coordinate;
  accuracy: number;
  source: CoordinateSystem;
  target: CoordinateSystem;
  transformMethod?: string; // 转换方法标识
  debugInfo?: any; // 调试信息
}

/**
 * 坐标系管理服务
 * 负责坐标系切换、坐标转换、图层配置管理
 */
@Injectable({
  providedIn: 'root'
})
export class CoordinateSystemService {
  // 当前激活的坐标系
  private currentCoordinateSystem$ = new BehaviorSubject<CoordinateSystem>(CoordinateSystem.CGCS2000);
  
  // 当前激活的底图ID
  private currentBaseMapId$ = new BehaviorSubject<string>('td_vec');
  
  // 坐标转换缓存
  private transformCache = new Map<string, CoordinateTransformResult>();
  
  // 缓存最大数量
  private readonly MAX_CACHE_SIZE = 1000;

  constructor() {}

  /**
   * 获取当前坐标系
   */
  getCurrentCoordinateSystem(): Observable<CoordinateSystem> {
    return this.currentCoordinateSystem$.asObservable();
  }

  /**
   * 获取当前坐标系值
   */
  getCurrentCoordinateSystemValue(): CoordinateSystem {
    return this.currentCoordinateSystem$.value;
  }

  /**
   * 获取当前底图ID
   */
  getCurrentBaseMapId(): Observable<string> {
    return this.currentBaseMapId$.asObservable();
  }

  /**
   * 获取当前底图ID值
   */
  getCurrentBaseMapIdValue(): string {
    return this.currentBaseMapId$.value;
  }

  /**
   * 切换底图并更新坐标系
   * @param baseMapId 底图ID
   */
  switchBaseMap(baseMapId: string): void {
    const coordinateSystem = BaseMapCoordinateMapping[baseMapId];
    if (coordinateSystem) {
      this.currentBaseMapId$.next(baseMapId);
      this.currentCoordinateSystem$.next(coordinateSystem);
      console.log(`🗺️ 底图切换: ${baseMapId}, 坐标系: ${coordinateSystem}`);
    }
  }

  /**
   * 根据当前底图获取对应的业务图层配置
   * @param layerIds 可选的图层ID过滤列表
   */
  getCurrentBusinessLayers(layerIds?: string[]): LayerOption[] {
    const currentCRS = this.getCurrentCoordinateSystemValue();
    const businessLayers = BusinessLayerConfigs[currentCRS] || BusinessLayerConfigs[CoordinateSystem.CGCS2000];
    
    if (layerIds && layerIds.length > 0) {
      return businessLayers.filter(layer => this.matchLayerByLogicalId(layer, layerIds));
    }
    
    return businessLayers;
  }

  /**
   * 通过逻辑ID匹配图层（忽略坐标系后缀）
   * @param layer 图层配置
   * @param layerIds 图层ID列表
   */
  private matchLayerByLogicalId(layer: LayerOption, layerIds: string[]): boolean {
    const logicalId = layer.id.replace(/_cgcs2000|_gcj02$/i, '');
    return layerIds.includes(logicalId);
  }

  /**
   * 坐标转换（支持WGS84、CGCS2000、GCJ02之间的转换）
   * @param coordinate 原始坐标
   * @param from 源坐标系
   * @param to 目标坐标系
   */
  transformCoordinate(coordinate: Coordinate, from: CoordinateSystem, to: CoordinateSystem): CoordinateTransformResult {
    // 如果坐标系相同，直接返回
    if (from === to) {
      return {
        coordinate,
        accuracy: 0,
        source: from,
        target: to,
        transformMethod: 'none',
        debugInfo: { message: '坐标系相同，无需转换' }
      };
    }

    // 生成缓存键
    const cacheKey = `${coordinate[0]},${coordinate[1]}_${from}_${to}`;

    // 检查缓存
    if (this.transformCache.has(cacheKey)) {
      const cachedResult = this.transformCache.get(cacheKey)!;
      console.log('🔄 使用缓存的坐标转换结果:', cachedResult);
      return cachedResult;
    }

    // 执行坐标转换
    let transformedCoordinate: Coordinate;
    let accuracy = 2; // 默认精度误差约2米
    let transformMethod = 'unknown';
    let debugInfo: any = {};

    // 支持多种坐标系转换
    if (from === CoordinateSystem.CGCS2000 && to === CoordinateSystem.GCJ02) {
      transformedCoordinate = this.cgcs2000ToGcj02(coordinate);
      transformMethod = 'cgcs2000_to_gcj02';
      accuracy = 2;
    } else if (from === CoordinateSystem.GCJ02 && to === CoordinateSystem.CGCS2000) {
      transformedCoordinate = this.gcj02ToCgcs2000(coordinate);
      transformMethod = 'gcj02_to_cgcs2000';
      accuracy = 2;
    } else {
      // 对于GPS定位（通常是WGS84），需要特殊处理
      // GPS定位结果通常接近WGS84，需要转换为目标坐标系
      if (to === CoordinateSystem.GCJ02) {
        // GPS(WGS84) -> GCJ02
        transformedCoordinate = this.wgs84ToGcj02(coordinate);
        transformMethod = 'wgs84_to_gcj02';
        accuracy = 3; // GPS转换精度稍低
        debugInfo = {
          message: 'GPS定位转换为GCJ02坐标系',
          originalCoordinate: coordinate,
          method: 'WGS84偏移算法'
        };
      } else if (to === CoordinateSystem.CGCS2000) {
        // GPS(WGS84) -> CGCS2000
        // WGS84与CGCS2000在中国境内差异很小，可以近似处理
        transformedCoordinate = coordinate;
        transformMethod = 'wgs84_to_cgcs2000_approximate';
        accuracy = 1; // 近似转换，精度较高
        debugInfo = {
          message: 'GPS定位近似等同于CGCS2000',
          note: 'WGS84与CGCS2000在中国境内差异小于1米'
        };
      } else {
        // 不支持的转换，返回原坐标
        transformedCoordinate = coordinate;
        transformMethod = 'unsupported';
        accuracy = 0;
        debugInfo = {
          message: `不支持的坐标转换: ${from} -> ${to}`,
          warning: '返回原始坐标，可能存在偏差'
        };
      }
    }

    const result: CoordinateTransformResult = {
      coordinate: transformedCoordinate,
      accuracy,
      source: from,
      target: to,
      transformMethod,
      debugInfo
    };

    // 添加到缓存
    this.addToCache(cacheKey, result);

    // 输出转换调试信息
    console.log('🔄 坐标转换完成:', {
      原始坐标: coordinate,
      源坐标系: from,
      目标坐标系: to,
      转换后坐标: transformedCoordinate,
      转换方法: transformMethod,
      精度误差: accuracy,
      调试信息: debugInfo
    });

    return result;
  }

  /**
   * 批量坐标转换
   * @param coordinates 坐标数组
   * @param from 源坐标系
   * @param to 目标坐标系
   */
  batchTransformCoordinates(coordinates: Coordinate[], from: CoordinateSystem, to: CoordinateSystem): CoordinateTransformResult[] {
    return coordinates.map(coord => this.transformCoordinate(coord, from, to));
  }

  /**
   * CGCS2000转GCJ02（简化算法）
   * 注意：这是简化的转换算法，实际项目中应使用更精确的七参数转换
   */
  private cgcs2000ToGcj02(coordinate: Coordinate): Coordinate {
    const [lon, lat] = coordinate;
    
    // 简化的偏移量计算（实际应使用更精确的算法）
    const dLat = this.transformLat(lon - 105.0, lat - 35.0);
    const dLon = this.transformLon(lon - 105.0, lat - 35.0);
    
    const radLat = lat / 180.0 * Math.PI;
    let magic = Math.sin(radLat);
    magic = 1 - 0.00669342162296594323 * magic * magic;
    const sqrtMagic = Math.sqrt(magic);
    
    const dLatFinal = (dLat * 180.0) / ((6378245.0 * (1 - 0.00669342162296594323)) / (magic * sqrtMagic) * Math.PI);
    const dLonFinal = (dLon * 180.0) / (6378245.0 / sqrtMagic * Math.cos(radLat) * Math.PI);
    
    return [lon + dLonFinal, lat + dLatFinal];
  }

  /**
   * GCJ02转CGCS2000（简化算法）
   */
  private gcj02ToCgcs2000(coordinate: Coordinate): Coordinate {
    const [lon, lat] = coordinate;

    // 使用迭代方法进行逆转换
    const gcj02 = this.cgcs2000ToGcj02([lon, lat]);
    const dLon = gcj02[0] - lon;
    const dLat = gcj02[1] - lat;

    return [lon - dLon, lat - dLat];
  }

  /**
   * WGS84转GCJ02（GPS定位专用）
   * 这是专门针对GPS定位结果的转换算法
   */
  private wgs84ToGcj02(coordinate: Coordinate): Coordinate {
    const [lon, lat] = coordinate;

    // 判断是否在中国境内
    if (!this.isInChina(lon, lat)) {
      // 不在中国境内，不需要偏移
      return coordinate;
    }

    // 使用改进的WGS84到GCJ02转换算法
    const dLat = this.transformLat(lon - 105.0, lat - 35.0);
    const dLon = this.transformLon(lon - 105.0, lat - 35.0);

    const radLat = lat / 180.0 * Math.PI;
    let magic = Math.sin(radLat);
    magic = 1 - 0.00669342162296594323 * magic * magic;
    const sqrtMagic = Math.sqrt(magic);

    const dLatFinal = (dLat * 180.0) / ((6378245.0 * (1 - 0.00669342162296594323)) / (magic * sqrtMagic) * Math.PI);
    const dLonFinal = (dLon * 180.0) / (6378245.0 / sqrtMagic * Math.cos(radLat) * Math.PI);

    return [lon + dLonFinal, lat + dLatFinal];
  }

  /**
   * 判断坐标是否在中国境内
   * @param lon 经度
   * @param lat 纬度
   */
  private isInChina(lon: number, lat: number): boolean {
    // 中国境内的大致范围
    return lon >= 72.004 && lon <= 137.8347 && lat >= 0.8293 && lat <= 55.8271;
  }

  /**
   * 纬度转换辅助函数
   */
  private transformLat(lon: number, lat: number): number {
    let ret = -100.0 + 2.0 * lon + 3.0 * lat + 0.2 * lat * lat + 0.1 * lon * lat + 0.2 * Math.sqrt(Math.abs(lon));
    ret += (20.0 * Math.sin(6.0 * lon * Math.PI) + 20.0 * Math.sin(2.0 * lon * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * 经度转换辅助函数
   */
  private transformLon(lon: number, lat: number): number {
    let ret = 300.0 + lon + 2.0 * lat + 0.1 * lon * lon + 0.1 * lon * lat + 0.1 * Math.sqrt(Math.abs(lon));
    ret += (20.0 * Math.sin(6.0 * lon * Math.PI) + 20.0 * Math.sin(2.0 * lon * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lon * Math.PI) + 40.0 * Math.sin(lon / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (150.0 * Math.sin(lon / 12.0 * Math.PI) + 300.0 * Math.sin(lon / 30.0 * Math.PI)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * 添加到缓存
   */
  private addToCache(key: string, result: CoordinateTransformResult): void {
    // 如果缓存已满，删除最旧的条目
    if (this.transformCache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.transformCache.keys().next().value;
      this.transformCache.delete(firstKey);
    }
    
    this.transformCache.set(key, result);
  }

  /**
   * 清空转换缓存
   */
  clearTransformCache(): void {
    this.transformCache.clear();
  }

  /**
   * 验证坐标有效性
   * @param coordinate 坐标
   */
  isValidCoordinate(coordinate: Coordinate): boolean {
    if (!coordinate || coordinate.length < 2) {
      return false;
    }
    
    const [lon, lat] = coordinate;
    
    // 检查经纬度范围（中国境内）
    return lon >= 73 && lon <= 135 && lat >= 18 && lat <= 54;
  }

  /**
   * 获取坐标系显示名称
   */
  getCoordinateSystemDisplayName(crs: CoordinateSystem): string {
    switch (crs) {
      case CoordinateSystem.CGCS2000:
        return '国家2000';
      case CoordinateSystem.GCJ02:
        return '火星坐标';
      case CoordinateSystem.WGS84:
        return 'GPS坐标';
      default:
        return '未知坐标系';
    }
  }

  /**
   * 智能识别GPS定位的坐标系类型
   * GPS定位通常返回WGS84坐标，需要根据目标地图坐标系进行转换
   */
  transformGpsCoordinate(gpsCoordinate: Coordinate, targetCRS: CoordinateSystem): CoordinateTransformResult {
    return this.transformCoordinate(gpsCoordinate, CoordinateSystem.WGS84, targetCRS);
  }

  /**
   * 验证坐标转换结果的合理性
   * @param originalCoordinate 原始坐标
   * @param transformedCoordinate 转换后坐标
   * @param sourceCRS 源坐标系
   * @param targetCRS 目标坐标系
   */
  validateTransformResult(
    originalCoordinate: Coordinate,
    transformedCoordinate: Coordinate,
    sourceCRS: CoordinateSystem,
    targetCRS: CoordinateSystem
  ): { isValid: boolean; message: string; offset: number } {
    // 计算坐标偏移距离（米）
    const offset = this.calculateDistance(originalCoordinate, transformedCoordinate);

    let isValid = true;
    let message = '坐标转换正常';

    // 根据转换类型设置合理的偏移范围
    let maxOffset = 1000; // 默认最大偏移1000米

    if (sourceCRS === CoordinateSystem.WGS84 && targetCRS === CoordinateSystem.GCJ02) {
      maxOffset = 500; // WGS84到GCJ02偏移通常在几十到几百米
    } else if (sourceCRS === CoordinateSystem.WGS84 && targetCRS === CoordinateSystem.CGCS2000) {
      maxOffset = 10; // WGS84到CGCS2000偏移很小
    } else if (sourceCRS === CoordinateSystem.CGCS2000 && targetCRS === CoordinateSystem.GCJ02) {
      maxOffset = 500; // CGCS2000到GCJ02偏移较大
    }

    if (offset > maxOffset) {
      isValid = false;
      message = `坐标转换偏移过大: ${offset.toFixed(2)}米，超过预期范围${maxOffset}米`;
    } else if (offset > maxOffset * 0.8) {
      message = `坐标转换偏移较大: ${offset.toFixed(2)}米，接近预期上限`;
    } else {
      message = `坐标转换正常: 偏移${offset.toFixed(2)}米`;
    }

    return { isValid, message, offset };
  }

  /**
   * 计算两个坐标点之间的距离（米）
   * 使用Haversine公式
   */
  private calculateDistance(coord1: Coordinate, coord2: Coordinate): number {
    const [lon1, lat1] = coord1;
    const [lon2, lat2] = coord2;

    const R = 6371000; // 地球半径（米）
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }
}
