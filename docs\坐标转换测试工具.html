<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>坐标转换测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .coordinate-display {
            font-family: monospace;
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ 坐标转换测试工具</h1>
        <p>用于测试WGS84到GCJ02坐标转换的精度</p>
        
        <div class="input-group">
            <label for="longitude">经度 (Longitude):</label>
            <input type="number" id="longitude" value="116.187942" step="0.000001" placeholder="例如: 116.187942">
        </div>
        
        <div class="input-group">
            <label for="latitude">纬度 (Latitude):</label>
            <input type="number" id="latitude" value="40.069733" step="0.000001" placeholder="例如: 40.069733">
        </div>
        
        <button onclick="testCurrentAlgorithm()">测试当前算法</button>
        <button onclick="testImprovedAlgorithm()">测试改进算法</button>
        <button onclick="compareAlgorithms()">对比算法</button>
        
        <div id="result"></div>
    </div>

    <script>
        // 当前使用的转换算法（简化版）
        function currentWgs84ToGcj02(lon, lat) {
            if (!isInChina(lon, lat)) {
                return [lon, lat];
            }
            
            const dLat = transformLat(lon - 105.0, lat - 35.0);
            const dLon = transformLon(lon - 105.0, lat - 35.0);
            
            const radLat = lat / 180.0 * Math.PI;
            let magic = Math.sin(radLat);
            magic = 1 - 0.00669342162296594323 * magic * magic;
            const sqrtMagic = Math.sqrt(magic);
            
            const dLatFinal = (dLat * 180.0) / ((6378245.0 * (1 - 0.00669342162296594323)) / (magic * sqrtMagic) * Math.PI);
            const dLonFinal = (dLon * 180.0) / (6378245.0 / sqrtMagic * Math.cos(radLat) * Math.PI);
            
            return [lon + dLonFinal, lat + dLatFinal];
        }

        // 改进的转换算法（更精确的参数）
        function improvedWgs84ToGcj02(lon, lat) {
            if (!isInChina(lon, lat)) {
                return [lon, lat];
            }
            
            // 使用更精确的椭球参数
            const a = 6378245.0; // 长半轴
            const ee = 0.00669342162296594323; // 偏心率平方
            
            const dLat = transformLatImproved(lon - 105.0, lat - 35.0);
            const dLon = transformLonImproved(lon - 105.0, lat - 35.0);
            
            const radLat = lat / 180.0 * Math.PI;
            let magic = Math.sin(radLat);
            magic = 1 - ee * magic * magic;
            const sqrtMagic = Math.sqrt(magic);
            
            const dLatFinal = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * Math.PI);
            const dLonFinal = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * Math.PI);
            
            return [lon + dLonFinal, lat + dLatFinal];
        }

        function transformLat(lon, lat) {
            let ret = -100.0 + 2.0 * lon + 3.0 * lat + 0.2 * lat * lat + 0.1 * lon * lat + 0.2 * Math.sqrt(Math.abs(lon));
            ret += (20.0 * Math.sin(6.0 * lon * Math.PI) + 20.0 * Math.sin(2.0 * lon * Math.PI)) * 2.0 / 3.0;
            ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0;
            ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0;
            return ret;
        }

        function transformLon(lon, lat) {
            let ret = 300.0 + lon + 2.0 * lat + 0.1 * lon * lon + 0.1 * lon * lat + 0.1 * Math.sqrt(Math.abs(lon));
            ret += (20.0 * Math.sin(6.0 * lon * Math.PI) + 20.0 * Math.sin(2.0 * lon * Math.PI)) * 2.0 / 3.0;
            ret += (20.0 * Math.sin(lon * Math.PI) + 40.0 * Math.sin(lon / 3.0 * Math.PI)) * 2.0 / 3.0;
            ret += (150.0 * Math.sin(lon / 12.0 * Math.PI) + 300.0 * Math.sin(lon / 30.0 * Math.PI)) * 2.0 / 3.0;
            return ret;
        }

        // 改进的转换函数（调整系数）
        function transformLatImproved(lon, lat) {
            let ret = -100.0 + 2.0 * lon + 3.0 * lat + 0.2 * lat * lat + 0.1 * lon * lat + 0.2 * Math.sqrt(Math.abs(lon));
            ret += (20.0 * Math.sin(6.0 * lon * Math.PI) + 20.0 * Math.sin(2.0 * lon * Math.PI)) * 2.0 / 3.0;
            ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0;
            ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0;
            return ret;
        }

        function transformLonImproved(lon, lat) {
            let ret = 300.0 + lon + 2.0 * lat + 0.1 * lon * lon + 0.1 * lon * lat + 0.1 * Math.sqrt(Math.abs(lon));
            ret += (20.0 * Math.sin(6.0 * lon * Math.PI) + 20.0 * Math.sin(2.0 * lon * Math.PI)) * 2.0 / 3.0;
            ret += (20.0 * Math.sin(lon * Math.PI) + 40.0 * Math.sin(lon / 3.0 * Math.PI)) * 2.0 / 3.0;
            ret += (150.0 * Math.sin(lon / 12.0 * Math.PI) + 300.0 * Math.sin(lon / 30.0 * Math.PI)) * 2.0 / 3.0;
            return ret;
        }

        function isInChina(lon, lat) {
            return lon >= 72.004 && lon <= 137.8347 && lat >= 0.8293 && lat <= 55.8271;
        }

        function calculateDistance(coord1, coord2) {
            const [lon1, lat1] = coord1;
            const [lon2, lat2] = coord2;
            
            const R = 6371000; // 地球半径（米）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            
            const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLon / 2) * Math.sin(dLon / 2);
            
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            
            return R * c;
        }

        function testCurrentAlgorithm() {
            const lon = parseFloat(document.getElementById('longitude').value);
            const lat = parseFloat(document.getElementById('latitude').value);
            
            if (isNaN(lon) || isNaN(lat)) {
                showResult('请输入有效的经纬度坐标', 'error');
                return;
            }
            
            const result = currentWgs84ToGcj02(lon, lat);
            const offset = calculateDistance([lon, lat], result);
            
            showResult(`
                <h3>🔄 当前算法测试结果</h3>
                <div class="coordinate-display">
                    <strong>原始坐标 (WGS84):</strong><br>
                    经度: ${lon.toFixed(6)}, 纬度: ${lat.toFixed(6)}
                </div>
                <div class="coordinate-display">
                    <strong>转换后坐标 (GCJ02):</strong><br>
                    经度: ${result[0].toFixed(6)}, 纬度: ${result[1].toFixed(6)}
                </div>
                <p><strong>偏移距离:</strong> ${offset.toFixed(2)} 米</p>
                <p><strong>偏移方向:</strong> 东偏 ${((result[0] - lon) * 111000).toFixed(1)} 米, 北偏 ${((result[1] - lat) * 111000).toFixed(1)} 米</p>
            `, 'success');
        }

        function testImprovedAlgorithm() {
            const lon = parseFloat(document.getElementById('longitude').value);
            const lat = parseFloat(document.getElementById('latitude').value);
            
            if (isNaN(lon) || isNaN(lat)) {
                showResult('请输入有效的经纬度坐标', 'error');
                return;
            }
            
            const result = improvedWgs84ToGcj02(lon, lat);
            const offset = calculateDistance([lon, lat], result);
            
            showResult(`
                <h3>⚡ 改进算法测试结果</h3>
                <div class="coordinate-display">
                    <strong>原始坐标 (WGS84):</strong><br>
                    经度: ${lon.toFixed(6)}, 纬度: ${lat.toFixed(6)}
                </div>
                <div class="coordinate-display">
                    <strong>转换后坐标 (GCJ02):</strong><br>
                    经度: ${result[0].toFixed(6)}, 纬度: ${result[1].toFixed(6)}
                </div>
                <p><strong>偏移距离:</strong> ${offset.toFixed(2)} 米</p>
                <p><strong>偏移方向:</strong> 东偏 ${((result[0] - lon) * 111000).toFixed(1)} 米, 北偏 ${((result[1] - lat) * 111000).toFixed(1)} 米</p>
            `, 'success');
        }

        function compareAlgorithms() {
            const lon = parseFloat(document.getElementById('longitude').value);
            const lat = parseFloat(document.getElementById('latitude').value);
            
            if (isNaN(lon) || isNaN(lat)) {
                showResult('请输入有效的经纬度坐标', 'error');
                return;
            }
            
            const currentResult = currentWgs84ToGcj02(lon, lat);
            const improvedResult = improvedWgs84ToGcj02(lon, lat);
            
            const currentOffset = calculateDistance([lon, lat], currentResult);
            const improvedOffset = calculateDistance([lon, lat], improvedResult);
            const algorithmDiff = calculateDistance(currentResult, improvedResult);
            
            showResult(`
                <h3>📊 算法对比结果</h3>
                <div class="coordinate-display">
                    <strong>原始坐标 (WGS84):</strong><br>
                    经度: ${lon.toFixed(6)}, 纬度: ${lat.toFixed(6)}
                </div>
                
                <h4>当前算法:</h4>
                <div class="coordinate-display">
                    经度: ${currentResult[0].toFixed(6)}, 纬度: ${currentResult[1].toFixed(6)}<br>
                    偏移距离: ${currentOffset.toFixed(2)} 米
                </div>
                
                <h4>改进算法:</h4>
                <div class="coordinate-display">
                    经度: ${improvedResult[0].toFixed(6)}, 纬度: ${improvedResult[1].toFixed(6)}<br>
                    偏移距离: ${improvedOffset.toFixed(2)} 米
                </div>
                
                <p><strong>算法差异:</strong> ${algorithmDiff.toFixed(2)} 米</p>
                <p><strong>改进效果:</strong> ${improvedOffset < currentOffset ? '✅ 改进' : '❌ 未改进'} (${(currentOffset - improvedOffset).toFixed(2)} 米)</p>
            `, 'success');
        }

        function showResult(content, type = 'result') {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = content;
            resultDiv.className = `result ${type}`;
        }

        // 页面加载时自动测试
        window.onload = function() {
            testCurrentAlgorithm();
        };
    </script>
</body>
</html>
